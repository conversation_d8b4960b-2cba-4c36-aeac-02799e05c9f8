import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInterestedCreatorApplication } from "__tests__/factories/interestedCreators/InterestedCreatorApplication";
import ApplicationRejected from "../../../pages/interested-creators/application-rejected";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";
import { ApplicationRejectedPageLabels } from "@src/contentManagement/ApplicationRejectedPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import InterestedCreatorApplicationStatus from "@src/interestedCreators/InterestedCreatorApplicationStatus";

jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("InterestedCreatorsApplicationRejected", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const mockCheckedApplicationStatus = jest.fn();
  const analytics = {
    checkedApplicationStatus: mockCheckedApplicationStatus
  } as unknown as BrowserAnalytics;

  const basePageLabels = {
    applicationRejectedLabels: {
      pageTitle: "Application Rejected",
      title: "Application Not Approved",
      description: "Unfortunately, your application was not approved"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as ApplicationRejectedPageLabels & CommonPageLabels;

  const testApplication = anInterestedCreatorApplication() as InterestedCreatorApplicationStatus;

  const baseApplicationRejectedProps = {
    locale,
    pageLabels: basePageLabels,
    application: testApplication,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale,
      push: mockPush
    }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { FLAG_INTERESTED_CREATOR_CAN_APPLY: false }
    });
  });

  it("shows remote application rejected component", async () => {
    render(<ApplicationRejected {...baseApplicationRejectedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with document title configuration", async () => {
    render(<ApplicationRejected {...baseApplicationRejectedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with analytics configuration", async () => {
    render(<ApplicationRejected {...baseApplicationRejectedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows header with correct labels", async () => {
    render(<ApplicationRejected {...baseApplicationRejectedProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...baseApplicationRejectedProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<ApplicationRejected {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with FLAG_INTERESTED_CREATOR_CAN_APPLY enabled", async () => {
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { FLAG_INTERESTED_CREATOR_CAN_APPLY: true }
    });

    render(<ApplicationRejected {...baseApplicationRejectedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("passes correct props to ApplicationRejectedPage component", async () => {
    render(<ApplicationRejected {...baseApplicationRejectedProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    expect(dynamicComponent).toBeInTheDocument();

    // Verify the component receives the expected props structure
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });

  it("renders with different locale", async () => {
    const propsWithDifferentLocale = {
      ...baseApplicationRejectedProps,
      locale: "fr-fr"
    };

    render(<ApplicationRejected {...propsWithDifferentLocale} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("handles missing page labels gracefully", () => {
    const propsWithMinimalLabels = {
      ...baseApplicationRejectedProps,
      pageLabels: {
        applicationRejectedLabels: {
          pageTitle: "Test Title"
        },
        commonPageLabels: {
          creatorNetwork: "Network",
          close: "Close"
        }
      } as ApplicationRejectedPageLabels & CommonPageLabels
    };

    expect(() => render(<ApplicationRejected {...propsWithMinimalLabels} />)).not.toThrow();
  });

  it("renders with default analytics when not provided", () => {
    const propsWithoutAnalytics = {
      ...baseApplicationRejectedProps,
      analytics: undefined
    };

    expect(() => render(<ApplicationRejected {...propsWithoutAnalytics} />)).not.toThrow();
  });

  it("renders layout components correctly", async () => {
    render(<ApplicationRejected {...baseApplicationRejectedProps} />);

    // Check for layout structure
    const container = document.querySelector('.mg-container');
    expect(container).toBeInTheDocument();

    const background = document.querySelector('.mg-bg');
    expect(background).toBeInTheDocument();
  });
});
