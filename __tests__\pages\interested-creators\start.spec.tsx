import React from "react";
import { useRouter } from "next/router";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ApplicationStart from "../../../pages/interested-creators/start";
import { useDependency } from "@src/context/DependencyContext";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");

describe("Interested Creator Start Page", () => {
  const mockPush = jest.fn();
  const router = { locale: "en-us", push: mockPush };
  const mockStartedCreatorApplication = jest.fn();
  const analytics = {
    checkedApplicationStatus: jest.fn(),
    startedCreatorApplication: mockStartedCreatorApplication
  } as unknown as BrowserAnalytics;

  const baseApplicationStartProps = {
    pageLabels: {
      applicationStartPageLabels: {
        title: "Start Your Application",
        subTitle: "Join the Creator Network",
        description: "Apply to become a creator",
        descriptionSuffix: "and start earning",
        alreadyApplied: "Already applied?",
        alreadyAppliedSuffix: "Check your status",
        explore: "Explore",
        exploreLeftTitle: "How it works",
        exploreRightTitle: "Opportunities",
        perks: "View perks"
      },
      informationLabels: {
        interestedCreatorTitle: "Start Page"
      },
      commonPageLabels: {
        applyNow: "Apply Now",
        creatorNetwork: "Creator Network",
        close: "Close",
        how: "How it works",
        unhandledError: "An error occurred"
      }
    } as ApplicationStartPageLabels & CommonPageLabels & InformationPageLabels,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        APPLICATIONS_MFE_BASE_URL: "http://localhost:3003",
        FLAG_PER_PROGRAM_PROFILE: false
      }
    });
  });

  it("shows tab title as 'Start your submission'", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    await waitFor(() => {
      expect(document.title).toMatch(/Start Page/);
    });
  });

  it("shows remote Information component", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("calls analytics when start application is triggered", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    // Wait for the component to render
    await screen.findByTestId("dynamic");

    // Simulate the start application action
    const startApplicationButton = screen.getByRole("button", { name: /apply now/i });
    await userEvent.click(startApplicationButton);

    expect(mockStartedCreatorApplication).toHaveBeenCalledWith({
      locale: "en-us",
      page: expect.any(String)
    });
  });

  it("navigates to applications URL when start application is clicked", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    await screen.findByTestId("dynamic");

    const startApplicationButton = screen.getByRole("button", { name: /apply now/i });
    await userEvent.click(startApplicationButton);

    expect(mockPush).toHaveBeenCalledWith("http://localhost:3003/api/applications");
  });

  it("navigates to per-program profile URL when FLAG_PER_PROGRAM_PROFILE is enabled", async () => {
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        APPLICATIONS_MFE_BASE_URL: "http://localhost:3003",
        FLAG_PER_PROGRAM_PROFILE: true
      }
    });

    render(<ApplicationStart {...baseApplicationStartProps} />);

    await screen.findByTestId("dynamic");

    const startApplicationButton = screen.getByRole("button", { name: /apply now/i });
    await userEvent.click(startApplicationButton);

    expect(mockPush).toHaveBeenCalledWith("/api/applications");
  });

  it("navigates to home page when close button is clicked", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    const closeButton = screen.getByRole("button", { name: /close/i });
    await userEvent.click(closeButton);

    expect(mockPush).toHaveBeenCalledWith("/");
  });

  it("displays explore pages with correct links", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(screen.getByText("How it works")).toBeInTheDocument();
    expect(screen.getByText("Opportunities")).toBeInTheDocument();

    const howItWorksLink = screen.getByRole("link", { name: /how it works/i });
    const opportunitiesLink = screen.getByRole("link", { name: /view perks/i });

    expect(howItWorksLink).toHaveAttribute("href", "/how-it-works");
    expect(opportunitiesLink).toHaveAttribute("href", "/opportunities-rewards");
  });

  it("renders with default analytics when not provided", () => {
    const propsWithoutAnalytics = {
      ...baseApplicationStartProps,
      analytics: undefined
    };

    expect(() => render(<ApplicationStart {...propsWithoutAnalytics} />)).not.toThrow();
  });

  it("displays all required labels correctly", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(screen.getByText("Start Your Application")).toBeInTheDocument();
    expect(screen.getByText("Join the Creator Network")).toBeInTheDocument();
    expect(screen.getByText("Apply to become a creator")).toBeInTheDocument();
    expect(screen.getByText("and start earning")).toBeInTheDocument();
    expect(screen.getByText("Explore")).toBeInTheDocument();
  });
});
