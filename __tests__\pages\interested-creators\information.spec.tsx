import "reflect-metadata";
import React from "react";
import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { mockMatchMedia } from "../../helpers/window";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";
import { renderPage } from "__tests__/helpers/page";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { AddContentPageLabels } from "@src/contentManagement/AddContentPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/contentManagement/ConnectAccountsPageMapper";
import Information from "pages/interested-creators/information";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");

describe("InterestedCreators", () => {
  const mockPush = jest.fn();
  const mockDispatch = jest.fn();
  const mockCancelledCreatorApplication = jest.fn();
  const router = { locale: "en-us", push: mockPush };

  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };

  const mockAnalytics = {
    cancelledCreatorApplication: mockCancelledCreatorApplication
  } as unknown as BrowserAnalytics;

  const baseInterestedCreatorsProps = {
    interestedCreator: initialInterestedCreator,
    analytics: mockAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: {
      informationLabels: {
        messages: {
          email: "Email"
        },
        informationPageTitle: "Information Page",
        interestedCreatorTitle: "Creator Information"
      },
      addContentPageLabels: {},
      breadcrumbPageLabels: {},
      communicationPreferencesPageLabels: {
        messages: {},
        labels: {}
      },
      commonPageLabels: {
        back: "Back",
        creatorNetwork: "Creator Network",
        close: "Close",
        information: "Information",
        franchises: "Franchises",
        creatorType: "Creator Type",
        unhandledError: "An error occurred"
      },
      connectAccountsLabels: {
        title: "Connect Accounts",
        description: "Connect your social media accounts"
      }
    } as InformationPageLabels &
      CommonPageLabels &
      BreadcrumbPageLabels &
      AddContentPageLabels &
      CommunicationPreferencesPageLabels &
      ConnectAccountsPageLabels,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pages: [],
    FLAG_COUNTRIES_BY_TYPE: false
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];

  mockMatchMedia();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: false
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      metadataClient: {},
      configuration: { APPLICATIONS_MFE_BASE_URL: "http://localhost:3003", BASE_PATH: "/support-a-creator" }
    });
  });

  it("shows remote information component", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("calls analytics when logout is triggered", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    await screen.findByTestId("dynamic");

    // Simulate logout action
    const closeButton = screen.getByRole("button", { name: /close/i });
    await userEvent.click(closeButton);

    expect(mockCancelledCreatorApplication).toHaveBeenCalledWith({
      locale: "en-us",
      page: expect.any(String)
    });
  });

  it("navigates to logout when close is clicked", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    await screen.findByTestId("dynamic");

    const closeButton = screen.getByRole("button", { name: /close/i });
    await userEvent.click(closeButton);

    expect(mockPush).toHaveBeenCalledWith("/api/logout");
  });

  it("renders with exception code and shows error page", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: 500,
        sessionUser: { id: "test-user" },
        isLoading: false
      }
    });

    renderPage(<Information {...baseInterestedCreatorsProps} />);

    // Should render error page instead of information component
    expect(screen.queryByTestId("dynamic")).not.toBeInTheDocument();
  });

  it("displays loading state when isLoading is true", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: true
      }
    });

    renderPage(<Information {...baseInterestedCreatorsProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with different page labels", async () => {
    const propsWithDifferentLabels = {
      ...baseInterestedCreatorsProps,
      pageLabels: {
        ...baseInterestedCreatorsProps.pageLabels,
        informationLabels: {
          ...baseInterestedCreatorsProps.pageLabels.informationLabels,
          informationPageTitle: "Custom Information Title"
        }
      }
    };

    renderPage(<Information {...propsWithDifferentLabels} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("handles confirmation modal state changes", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    await screen.findByTestId("dynamic");

    // The component should handle confirmation modal state internally
    expect(screen.getByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...baseInterestedCreatorsProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    renderPage(<Information {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with FLAG_COUNTRIES_BY_TYPE enabled", async () => {
    const propsWithCountriesByType = {
      ...baseInterestedCreatorsProps,
      FLAG_COUNTRIES_BY_TYPE: true
    };

    renderPage(<Information {...propsWithCountriesByType} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("passes correct connect accounts configuration", async () => {
    renderPage(<Information {...baseInterestedCreatorsProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    expect(dynamicComponent).toBeInTheDocument();

    // Verify the component receives connect accounts data
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });
});
