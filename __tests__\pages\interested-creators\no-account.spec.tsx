import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";
import { useDependency } from "@src/context/DependencyContext";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import InterestedCreator from "@src/interestedCreators/InterestedCreator";
import { NoAccountLabels } from "@src/contentManagement/NoAccountPageMapper";
import NoAccount, { NoAccountType } from "pages/interested-creators/no-account";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";

jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("InterestedCreatorsNoAccount", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const router = { locale, push: mockPush };

  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };

  const baseNoAccountProps = {
    locale,
    interestedCreator: anInitialInterestedCreator() as InterestedCreator,
    analytics: {} as unknown as BrowserAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: {
      noAccountLabels: {
        pageTitle: "No Account Found",
        title: "Account Not Found",
        description: "We couldn't find your account"
      },
      commonPageLabels: {
        creatorNetwork: "Creator Network",
        close: "Close"
      },
      applicationStartPageLabels: {
        title: "Get Started"
      }
    } as unknown as NoAccountLabels & CommonPageLabels & ApplicationStartPageLabels
  } as NoAccountType;

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { APPLICATIONS_MFE_BASE_URL: "http://localhost:3003" }
    });
  });

  it("shows remote no account component", async () => {
    render(<NoAccount {...baseNoAccountProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with different locale", async () => {
    const propsWithDifferentLocale = {
      ...baseNoAccountProps,
      locale: "fr-fr"
    };

    render(<NoAccount {...propsWithDifferentLocale} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with different configuration", async () => {
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { APPLICATIONS_MFE_BASE_URL: "http://different-url.com" }
    });

    render(<NoAccount {...baseNoAccountProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with different interested creator", async () => {
    const differentInterestedCreator = anInitialInterestedCreator({
      nucleusId: 999999,
      originEmail: "<EMAIL>"
    }) as InterestedCreator;

    const propsWithDifferentCreator = {
      ...baseNoAccountProps,
      interestedCreator: differentInterestedCreator
    };

    render(<NoAccount {...propsWithDifferentCreator} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows header with correct labels", async () => {
    render(<NoAccount {...baseNoAccountProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
  });

  it("shows component with minimal page labels", () => {
    const propsWithMinimalLabels = {
      ...baseNoAccountProps,
      pageLabels: {
        noAccountLabels: {
          pageTitle: "No Account"
        },
        commonPageLabels: {
          creatorNetwork: "Network",
          close: "Close"
        },
        applicationStartPageLabels: {
          title: "Start"
        }
      } as unknown as NoAccountLabels & CommonPageLabels & ApplicationStartPageLabels
    };

    expect(() => render(<NoAccount {...propsWithMinimalLabels} />)).not.toThrow();
  });
});
