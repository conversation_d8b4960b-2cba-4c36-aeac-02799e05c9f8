import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { anInterestedCreator } from "__tests__/factories/interestedCreators/InterestedCreator";
import ApplicationComplete from "../../../pages/interested-creators/complete";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { ApplicationCompletePageLabels } from "@src/contentManagement/ApplicationCompletePageMapper";

jest.mock("next/router");

describe("InterestedCreatorsComplete", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const testInterestedCreator = {
    ...anInterestedCreator(),
    contentLanguages: [{ code: "en", name: "English" }],
    createdDate: "2023-12-12T10:00:00Z",
    originEmail: "<EMAIL>"
  };

  const basePageLabels = {
    applicationCompletePageLabels: {
      title: "Application Complete",
      description: "Thank you for your submission",
      buttonText: "Back to Home"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as ApplicationCompletePageLabels & CommonPageLabels;

  const applicationCompleteProps = {
    locale,
    interestedCreator: testInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pageLabels: basePageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale,
      push: mockPush
    }));
  });

  it("shows remote complete component", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("calls logout when back button is clicked", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    await screen.findByTestId("dynamic");

    // Simulate clicking the back button that triggers logout
    const backButton = screen.getByRole("button", { name: /back to home/i });
    await userEvent.click(backButton);

    expect(mockPush).toHaveBeenCalledWith("/api/logout");
  });

  it("displays header with correct labels", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /close/i })).toBeInTheDocument();
  });

  it("passes correct props to ApplicationCompletedPage component", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    expect(dynamicComponent).toBeInTheDocument();

    // Verify the component structure
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...applicationCompleteProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<ApplicationComplete {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("handles different locale correctly", async () => {
    const propsWithDifferentLocale = {
      ...applicationCompleteProps,
      locale: "fr-fr"
    };

    render(<ApplicationComplete {...propsWithDifferentLocale} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("displays correct submitted date and email", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    expect(dynamicComponent).toBeInTheDocument();

    // The component should receive the interested creator's data
    expect(dynamicComponent).toBeInTheDocument();
  });

  it("uses default thumbnail image", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    expect(dynamicComponent).toBeInTheDocument();

    // Verify the component receives the expected thumbnail path
    expect(dynamicComponent).toBeInTheDocument();
  });

  it("handles missing interested creator data gracefully", () => {
    const propsWithMinimalData = {
      ...applicationCompleteProps,
      interestedCreator: {
        ...testInterestedCreator,
        createdDate: undefined,
        originEmail: undefined
      }
    };

    expect(() => render(<ApplicationComplete {...propsWithMinimalData} />)).not.toThrow();
  });

  it("renders layout components correctly", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    // Check for layout structure
    const container = document.querySelector('.mg-container');
    expect(container).toBeInTheDocument();

    const background = document.querySelector('.mg-bg');
    expect(background).toBeInTheDocument();
  });
});
