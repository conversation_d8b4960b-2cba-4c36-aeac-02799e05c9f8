import React from "react";
import { useRouter } from "next/router";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ApplicationAccepted from "../../../pages/interested-creators/application-accepted";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { ApplicationAcceptedPageLabels } from "@src/contentManagement/ApplicationAcceptedPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

jest.mock("next/router");

describe("InterestedCreatorsApplicationAccepted", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const analytics = {
    checkedApplicationStatus: jest.fn()
  } as unknown as BrowserAnalytics;

  const basePageLabels = {
    applicationAcceptedLabels: {
      pageTitle: "Application Accepted",
      title: "Congratulations!",
      description: "Your application has been accepted",
      buttonText: "Get Started"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as ApplicationAcceptedPageLabels & CommonPageLabels;

  const applicationAcceptedProps = {
    pageLabels: basePageLabels,
    locale,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale,
      push: mockPush
    }));
  });

  it("shows tab title as 'Application Accepted'", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(document.title).toMatch(/Application Accepted/);
  });

  it("shows remote accepted component", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("navigates to onboarding information page when button is clicked", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    await screen.findByTestId("dynamic");

    // Simulate clicking the button that triggers navigation
    const getStartedButton = screen.getByRole("button", { name: /get started/i });
    await userEvent.click(getStartedButton);

    expect(mockPush).toHaveBeenCalledWith("/onboarding/information");
  });

  it("renders with default analytics when not provided", () => {
    const propsWithoutAnalytics = {
      ...applicationAcceptedProps,
      analytics: undefined
    };

    expect(() => render(<ApplicationAccepted {...propsWithoutAnalytics} />)).not.toThrow();
  });

  it("displays header with correct labels", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /close/i })).toBeInTheDocument();
  });

  it("sets document title on component mount", async () => {
    const originalTitle = document.title;

    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    await waitFor(() => {
      expect(document.title).toBe("Application Accepted");
    });

    // Cleanup
    document.title = originalTitle;
  });

  it("passes correct props to ApplicationAcceptedPage component", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    expect(dynamicComponent).toBeInTheDocument();

    // Verify the component receives the expected props structure
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });

  it("renders with different locale", async () => {
    const propsWithDifferentLocale = {
      ...applicationAcceptedProps,
      locale: "fr-fr"
    };

    render(<ApplicationAccepted {...propsWithDifferentLocale} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("handles missing page labels gracefully", () => {
    const propsWithMinimalLabels = {
      ...applicationAcceptedProps,
      pageLabels: {
        applicationAcceptedLabels: {
          pageTitle: "Test Title"
        },
        commonPageLabels: {
          creatorNetwork: "Network",
          close: "Close"
        }
      } as ApplicationAcceptedPageLabels & CommonPageLabels
    };

    expect(() => render(<ApplicationAccepted {...propsWithMinimalLabels} />)).not.toThrow();
  });
});
